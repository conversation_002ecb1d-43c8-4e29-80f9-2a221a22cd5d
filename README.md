This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Environment Configuration

Before running the application, you need to set up your environment variables. Create a `.env.local` file in the root directory with the following variables:

```bash
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/database_name"

# Better Auth Configuration
BETTER_AUTH_URL="http://localhost:3000"
BETTER_AUTH_SECRET="your-secret-key-here"

# Google OAuth Configuration
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
NEXT_PUBLIC_GOOGLE_CLIENT_ID="your-google-client-id"

# App Configuration
NEXT_PUBLIC_APP_URL="http://localhost:3000"

# Google One Tap Configuration
# Set to 'true' to enable Google One Tap, 'false' to disable (default: false)
ENABLE_ONE_TAP="false"
NEXT_PUBLIC_ENABLE_ONE_TAP="false"
```

### Google One Tap Feature

This application includes Google One Tap functionality that can be controlled via environment variables:

- `ENABLE_ONE_TAP`: Controls server-side One Tap plugin initialization
- `NEXT_PUBLIC_ENABLE_ONE_TAP`: Controls client-side One Tap functionality

Both variables must be set to `"true"` to enable the Google One Tap feature. By default, the feature is disabled (`false`).

## Code Formatting

This project uses [Prettier](https://prettier.io/) for code formatting. The configuration is set up to work seamlessly with ESLint.

### Available Scripts

- `pnpm format` - Format all files in the project
- `pnpm format:check` - Check if all files are properly formatted
- `pnpm lint:fix` - Run ESLint with auto-fix

### Editor Integration

The project includes VS Code settings that automatically format files on save using Prettier. Make sure you have the [Prettier VS Code extension](https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode) installed.

### Configuration

- `.prettierrc` - Prettier configuration
- `.prettierignore` - Files to ignore during formatting
- `eslint.config.mjs` - ESLint configuration with Prettier integration

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
