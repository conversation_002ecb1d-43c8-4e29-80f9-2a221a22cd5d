{"name": "hal-nextjs-template", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@neondatabase/serverless": "^1.0.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-visually-hidden": "^1.2.3", "@t3-oss/env-nextjs": "^0.13.4", "@tanstack/react-query": "^5.75.0", "@trpc/client": "^11.1.2", "@trpc/react-query": "^11.1.2", "@trpc/server": "^11.1.2", "@types/qrcode": "^1.5.5", "better-auth": "^1.2.8", "class-variance-authority": "^0.7.1", "client-only": "^0.0.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "drizzle-orm": "^0.43.1", "lucide-react": "^0.503.0", "next": "15.3.1", "next-intl": "^4.1.0", "qrcode": "^1.5.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "server-only": "^0.0.1", "superjson": "^2.2.2", "tailwind-merge": "^3.2.0", "zod": "^3.24.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "drizzle-kit": "^0.31.0", "eslint": "^9", "eslint-config-next": "15.3.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "prettier": "^3.5.3", "tailwindcss": "^4", "tw-animate-css": "^1.2.8", "typescript": "^5"}}