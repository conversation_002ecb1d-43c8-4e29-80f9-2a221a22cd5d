ALTER TABLE "two_factor" ALTER COLUMN "secret" SET NOT NULL;--> statement-breakpoint
ALTER TABLE "two_factor" ALTER COLUMN "backup_codes" SET NOT NULL;--> statement-breakpoint
ALTER TABLE "user" ALTER COLUMN "two_factor_enabled" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "session" ADD COLUMN "impersonated_by" text;--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "role" text;--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "banned" boolean;--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "ban_reason" text;--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "ban_expires" timestamp;--> statement-breakpoint
ALTER TABLE "two_factor" DROP COLUMN "created_at";--> statement-breakpoint
ALTER TABLE "two_factor" DROP COLUMN "updated_at";