'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { signIn } from '@/lib/auth-client';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { GoogleIcon } from '@/components/ui/google-icon';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';

// Type definition for 2FA-enabled sign in response
interface SignInResponseData {
  redirect: boolean;
  token: string;
  url?: string;
  user: {
    id: string;
    email: string;
    name: string;
    image?: string | null;
    emailVerified: boolean;
    createdAt: Date;
    updatedAt: Date;
  };
  twoFactorRedirect?: boolean;
}

interface SignInSuccessContext {
  data: SignInResponseData;
}

interface SignInErrorContext {
  error: {
    message: string;
  };
}

const signInSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(1, 'Password is required'),
});

type SignInFormData = z.infer<typeof signInSchema>;

interface SignInFormProps {
  onSuccess?: () => void;
}

export function SignInForm({ onSuccess }: SignInFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);
  const [error, setError] = useState('');
  const router = useRouter();

  const form = useForm<SignInFormData>({
    resolver: zodResolver(signInSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const onSubmit = async (data: SignInFormData) => {
    setIsLoading(true);
    setError('');

    try {
      const result = await signIn.email(
        {
          email: data.email,
          password: data.password,
        },
        {
          onSuccess(context: SignInSuccessContext) {
            if (context.data.twoFactorRedirect) {
              // User has 2FA enabled, redirect to 2FA verification page
              router.push('/2fa');
            } else {
              // Normal sign in success
              router.refresh();
              onSuccess?.();
            }
          },
          onError(context: SignInErrorContext) {
            setError(context.error.message || 'Sign in failed');
          },
        }
      );

      // Handle the case where the result is returned directly (fallback)
      if (result?.error) {
        setError(result.error.message || 'Sign in failed');
      } else if ((result?.data as SignInResponseData)?.twoFactorRedirect) {
        router.push('/2fa');
      } else if (
        result?.data &&
        !(result.data as SignInResponseData).twoFactorRedirect
      ) {
        router.refresh();
        onSuccess?.();
      }
    } catch (err) {
      console.error(err);
      setError('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    setIsGoogleLoading(true);
    setError('');

    try {
      const result = await signIn.social({
        provider: 'google',
        callbackURL: '/',
      });

      if (result.error) {
        setError(result.error.message || 'Google sign in failed');
      } else {
        router.refresh();
        onSuccess?.();
      }
    } catch (err) {
      console.error(err);
      setError('An unexpected error occurred with Google sign in');
    } finally {
      setIsGoogleLoading(false);
    }
  };

  return (
    <div className='space-y-6'>
      <div className='space-y-2 text-center'>
        <h2 className='text-2xl font-semibold tracking-tight'>Welcome back</h2>
        <p className='text-sm text-muted-foreground'>
          Enter your credentials to sign in to your account
        </p>
      </div>

      {/* Google One Tap is now handled automatically on the login page */}

      {/* Google Sign In Button */}
      <Button
        type='button'
        variant='outline'
        className='w-full'
        onClick={handleGoogleSignIn}
        disabled={isGoogleLoading || isLoading}
      >
        <GoogleIcon className='mr-2 h-4 w-4' />
        {isGoogleLoading ? 'Signing in with Google...' : 'Continue with Google'}
      </Button>

      <div className='relative'>
        <div className='absolute inset-0 flex items-center'>
          <span className='w-full border-t' />
        </div>
        <div className='relative flex justify-center text-xs uppercase'>
          <span className='bg-background px-2 text-muted-foreground'>
            Or continue with
          </span>
        </div>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
          <FormField
            control={form.control}
            name='email'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input type='email' placeholder='<EMAIL>' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name='password'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Password</FormLabel>
                <FormControl>
                  <Input type='password' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          {error && <div className='text-destructive text-sm'>{error}</div>}
          <Button
            type='submit'
            className='w-full'
            disabled={isLoading || isGoogleLoading}
          >
            {isLoading ? 'Signing in...' : 'Sign In'}
          </Button>
        </form>
      </Form>
    </div>
  );
}
