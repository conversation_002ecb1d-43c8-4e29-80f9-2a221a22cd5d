'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { authClient } from '@/lib/auth-client';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

const verificationSchema = z.object({
  code: z.string().min(6, 'Code must be at least 6 characters'),
});

type VerificationFormData = z.infer<typeof verificationSchema>;

export function TwoFactorVerification() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('totp');
  const router = useRouter();

  const form = useForm<VerificationFormData>({
    resolver: zodResolver(verificationSchema),
    defaultValues: {
      code: '',
    },
  });

  const handleTotpVerification = async (data: VerificationFormData) => {
    setIsLoading(true);
    setError('');

    try {
      const result = await authClient.twoFactor.verifyTotp({
        code: data.code,
      });

      if (result.error) {
        setError(result.error.message || 'Verification failed');
      } else {
        router.push('/');
        router.refresh();
      }
    } catch (err) {
      console.error(err);
      setError('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackupCodeVerification = async (data: VerificationFormData) => {
    setIsLoading(true);
    setError('');

    try {
      const result = await authClient.twoFactor.verifyBackupCode({
        code: data.code,
      });

      if (result.error) {
        setError(result.error.message || 'Verification failed');
      } else {
        router.push('/');
        router.refresh();
      }
    } catch (err) {
      console.error(err);
      setError('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const onSubmit = async (data: VerificationFormData) => {
    if (activeTab === 'totp') {
      await handleTotpVerification(data);
    } else {
      await handleBackupCodeVerification(data);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Verify Your Identity</CardTitle>
        <CardDescription>
          Enter your verification code to complete sign in
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className='grid w-full grid-cols-2'>
            <TabsTrigger value='totp'>Authenticator App</TabsTrigger>
            <TabsTrigger value='backup'>Backup Code</TabsTrigger>
          </TabsList>

          <TabsContent value='totp' className='mt-4'>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className='space-y-4'
              >
                <FormField
                  control={form.control}
                  name='code'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Authentication Code</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder='Enter 6-digit code'
                          maxLength={6}
                          autoComplete='one-time-code'
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {error && (
                  <div className='text-sm text-red-600 bg-red-50 p-3 rounded-md'>
                    {error}
                  </div>
                )}

                <Button type='submit' className='w-full' disabled={isLoading}>
                  {isLoading ? 'Verifying...' : 'Verify Code'}
                </Button>
              </form>
            </Form>
          </TabsContent>

          <TabsContent value='backup' className='mt-4'>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className='space-y-4'
              >
                <FormField
                  control={form.control}
                  name='code'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Backup Code</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder='Enter backup code'
                          autoComplete='one-time-code'
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {error && (
                  <div className='text-sm text-red-600 bg-red-50 p-3 rounded-md'>
                    {error}
                  </div>
                )}

                <Button type='submit' className='w-full' disabled={isLoading}>
                  {isLoading ? 'Verifying...' : 'Use Backup Code'}
                </Button>
              </form>
            </Form>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
