'use client';

import { useEffect, useState } from 'react';
import { oneTap } from '@/lib/auth-client';
import { useRouter } from 'next/navigation';
import { env } from '@/env';

interface AutoOneTapProps {
  onSuccess?: () => void;
  callbackURL?: string;
}

export function AutoOneTap({ onSuccess, callbackURL = '/' }: AutoOneTapProps) {
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState('');
  const router = useRouter();

  useEffect(() => {
    // If One Tap is disabled, don't initialize
    if (!env.NEXT_PUBLIC_ENABLE_ONE_TAP) {
      console.log('Google One Tap is disabled via NEXT_PUBLIC_ENABLE_ONE_TAP');
      return;
    }
    // 只在组件首次挂载时初始化 One Tap
    if (isInitialized) return;

    const initializeOneTap = async () => {
      try {
        console.log(
          '🚀 Auto-initializing One Tap with client ID:',
          process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID
        );

        await oneTap({
          callbackURL,
          fetchOptions: {
            onSuccess: () => {
              console.log('✅ Auto One Tap authentication successful');
              router.refresh();
              onSuccess?.();
            },
            onError: error => {
              console.error('❌ Auto One Tap fetch error:', error);
              setError(`Authentication failed: ${JSON.stringify(error)}`);
            },
          },
          onPromptNotification: notification => {
            console.warn('⚠️ Auto One Tap prompt notification:', notification);
            const momentType = notification.getMomentType
              ? notification.getMomentType()
              : 'unknown';
            const reason = notification.getDismissedReason
              ? notification.getDismissedReason()
              : 'unknown';

            console.log(
              'Moment type:',
              momentType,
              'Dismissed reason:',
              reason
            );

            if (momentType === 'display' && reason === 'credential_returned') {
              // This is actually a success case
              return;
            }

            // 不显示错误，因为这是自动触发的
            console.log(`Auto One Tap prompt: ${momentType} - ${reason}`);
          },
        });

        setIsInitialized(true);
      } catch (err) {
        console.error('💥 Auto One Tap initialization error:', err);
        const errorMessage =
          err instanceof Error ? err.message : 'Unknown error occurred';
        setError(`Failed to initialize Auto One Tap: ${errorMessage}`);
        setIsInitialized(true);
      }
    };

    // 延迟一秒执行，让页面完全加载
    const timer = setTimeout(initializeOneTap, 1000);

    return () => {
      clearTimeout(timer);
    };
  }, [isInitialized, callbackURL, router, onSuccess]);

  // 这个组件不渲染任何可见内容，仅用于触发 One Tap
  return error ? (
    <div className='fixed bottom-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded max-w-sm text-sm'>
      {error}
    </div>
  ) : null;
}
