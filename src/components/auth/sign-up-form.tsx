'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { signUp, signIn } from '@/lib/auth-client';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { GoogleIcon } from '@/components/ui/google-icon';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';

const signUpSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
});

type SignUpFormData = z.infer<typeof signUpSchema>;

interface SignUpFormProps {
  onSuccess?: () => void;
}

export function SignUpForm({ onSuccess }: SignUpFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);
  const [error, setError] = useState('');
  const router = useRouter();

  const form = useForm<SignUpFormData>({
    resolver: zodResolver(signUpSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const onSubmit = async (data: SignUpFormData) => {
    setIsLoading(true);
    setError('');

    try {
      // Extract name from email prefix
      const name = data.email.split('@')[0];

      const result = await signUp.email({
        email: data.email,
        password: data.password,
        name: name,
      });

      if (result.error) {
        console.log(result);
        setError(result.error.message || 'Sign up failed');
      } else {
        router.refresh();
        onSuccess?.();
      }
    } catch (err) {
      console.error(err);
      setError('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleSignUp = async () => {
    setIsGoogleLoading(true);
    setError('');

    try {
      const result = await signIn.social({
        provider: 'google',
        callbackURL: '/',
      });

      if (result.error) {
        setError(result.error.message || 'Google sign up failed');
      } else {
        router.refresh();
        onSuccess?.();
      }
    } catch (err) {
      console.error(err);
      setError('An unexpected error occurred with Google sign up');
    } finally {
      setIsGoogleLoading(false);
    }
  };

  return (
    <div className='space-y-6'>
      <div className='space-y-2 text-center'>
        <h2 className='text-2xl font-semibold tracking-tight'>
          Create an account
        </h2>
        <p className='text-sm text-muted-foreground'>
          Enter your information to create your account
        </p>
      </div>

      {/* Google One Tap is now handled automatically on the login page */}

      {/* Google Sign Up Button */}
      <Button
        type='button'
        variant='outline'
        className='w-full'
        onClick={handleGoogleSignUp}
        disabled={isGoogleLoading || isLoading}
      >
        <GoogleIcon className='mr-2 h-4 w-4' />
        {isGoogleLoading
          ? 'Creating account with Google...'
          : 'Continue with Google'}
      </Button>

      <div className='relative'>
        <div className='absolute inset-0 flex items-center'>
          <span className='w-full border-t' />
        </div>
        <div className='relative flex justify-center text-xs uppercase'>
          <span className='bg-background px-2 text-muted-foreground'>
            Or continue with
          </span>
        </div>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
          <FormField
            control={form.control}
            name='email'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input type='email' placeholder='<EMAIL>' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name='password'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Password</FormLabel>
                <FormControl>
                  <Input type='password' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          {error && <div className='text-destructive text-sm'>{error}</div>}
          <Button
            type='submit'
            className='w-full'
            disabled={isLoading || isGoogleLoading}
          >
            {isLoading ? 'Creating account...' : 'Create Account'}
          </Button>
        </form>
      </Form>
    </div>
  );
}
