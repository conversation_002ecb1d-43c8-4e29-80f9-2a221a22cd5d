'use client';

import { useSession } from '@/lib/auth-client';
import { AuthButtons } from '@/components/auth/auth-buttons';

export function ConditionalHeader() {
  const { data: session } = useSession();

  // 如果用户已登录，显示头部
  if (session?.user) {
    return (
      <header className='flex justify-end items-center p-4 gap-4 h-16'>
        <AuthButtons />
      </header>
    );
  }

  // 如果正在加载或未登录，不显示头部
  return null;
}
