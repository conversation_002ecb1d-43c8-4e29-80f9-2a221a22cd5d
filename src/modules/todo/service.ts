import { and, eq } from 'drizzle-orm';
import { db } from '@/db';
import { todosTable } from '@/db/schema';

export const getTodos = async (userId: string) => {
  return await db
    .select()
    .from(todosTable)
    .where(eq(todosTable.userId, userId));
};

export const addTodo = async (userId: string, title: string) => {
  return await db.insert(todosTable).values({
    title,
    userId,
  });
};

export const updateTodo = async (userId: string, id: number, title: string) => {
  return await db
    .update(todosTable)
    .set({ title })
    .where(and(eq(todosTable.id, id), eq(todosTable.userId, userId)));
};

export const toggleTodo = async (
  userId: string,
  id: number,
  completed: boolean
) => {
  return await db
    .update(todosTable)
    .set({ completed })
    .where(and(eq(todosTable.id, id), eq(todosTable.userId, userId)))
    .returning();
};

export const deleteTodo = async (userId: string, id: number) => {
  return await db
    .delete(todosTable)
    .where(and(eq(todosTable.id, id), eq(todosTable.userId, userId)));
};
