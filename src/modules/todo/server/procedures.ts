import {
  baseProcedure,
  createTR<PERSON>Router,
  protectedProcedure,
} from '@/trpc/init';
import { z } from 'zod';
import {
  addTodo,
  deleteTodo,
  getTodos,
  toggleTodo,
  updateTodo,
} from '@/modules/todo/service';
export const todoRouter = createTRPCRouter({
  intro: baseProcedure.query(async () => {
    return {
      message: 'Hello World',
    };
  }),
  getTodos: protectedProcedure.query(async ({ ctx }) => {
    return await getTodos(ctx.userId);
  }),
  addTodo: protectedProcedure
    .input(
      z.object({
        title: z.string(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return await addTodo(ctx.userId, input.title);
    }),
  updateTodo: protectedProcedure
    .input(
      z.object({
        id: z.number(),
        title: z.string(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return await updateTodo(ctx.userId, input.id, input.title);
    }),
  toggleTodo: protectedProcedure
    .input(
      z.object({
        id: z.number(),
        completed: z.boolean(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return await toggleTodo(ctx.userId, input.id, input.completed);
    }),
  deleteTodo: protectedProcedure
    .input(
      z.object({
        id: z.number(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return await deleteTodo(ctx.userId, input.id);
    }),
});
