'use client';

import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AdminStats } from '@/components/admin/admin-stats';
import Link from 'next/link';

export default function AdminDashboard({
  params,
}: {
  params: { locale: string };
}) {
  return (
    <div className='space-y-8'>
      <AdminStats />

      <div className='grid gap-4 md:grid-cols-2'>
        <Card>
          <CardHeader>
            <CardTitle>User Management</CardTitle>
          </CardHeader>
          <CardContent className='space-y-4'>
            <p className='text-sm text-muted-foreground'>
              Manage user accounts, roles, and permissions.
            </p>
            <div className='flex gap-2'>
              <Button asChild>
                <Link href={`/${params.locale}/admin/users`}>
                  View All Users
                </Link>
              </Button>
              <Button variant='outline' asChild>
                <Link href={`/${params.locale}/admin/users/create`}>
                  Create User
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Session Management</CardTitle>
          </CardHeader>
          <CardContent className='space-y-4'>
            <p className='text-sm text-muted-foreground'>
              Monitor and manage user sessions.
            </p>
            <Button variant='outline' asChild>
              <Link href={`/${params.locale}/admin/sessions`}>
                View Sessions
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
