'use client';

import { UserTable } from '@/components/admin/user-table';

export default function AdminUsersPage({
  params,
}: {
  params: { locale: string };
}) {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">User Management</h2>
        <p className="text-muted-foreground">
          Manage user accounts, roles, and permissions.
        </p>
      </div>
      <UserTable locale={params.locale} />
    </div>
  );
}
