'use client';

import { CreateUserForm } from '@/components/admin/create-user-form';

export default function CreateUserPage({
  params,
}: {
  params: { locale: string };
}) {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Create User</h2>
        <p className="text-muted-foreground">
          Add a new user to the system.
        </p>
      </div>
      <CreateUserForm locale={params.locale} />
    </div>
  );
}
