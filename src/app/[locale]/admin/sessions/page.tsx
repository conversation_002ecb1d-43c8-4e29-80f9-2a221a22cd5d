'use client';

import { SessionTable } from '@/components/admin/session-table';

export default function AdminSessionsPage({
  params,
}: {
  params: { locale: string };
}) {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Session Management</h2>
        <p className="text-muted-foreground">
          Monitor and manage user sessions across the platform.
        </p>
      </div>
      <SessionTable locale={params.locale} />
    </div>
  );
}
