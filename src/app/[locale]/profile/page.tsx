import { auth } from '@/lib/auth';
import { headers } from 'next/headers';
import { redirect } from 'next/navigation';
import { ProfileForm } from '@/components/profile/profile-form';
import { PasswordForm } from '@/components/profile/password-form';
import { TwoFactorSettings } from '@/components/auth/two-factor-settings';

export default async function ProfilePage() {
  // Check if user is authenticated
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user) {
    redirect('/');
  }

  return (
    <div className='container mx-auto py-8 px-4 max-w-2xl'>
      <div className='mb-8'>
        <h1 className='text-3xl font-bold tracking-tight'>Profile Settings</h1>
        <p className='text-muted-foreground mt-2'>
          Manage your account settings and preferences.
        </p>
      </div>

      <div className='space-y-8'>
        <ProfileForm />
        <PasswordForm />
        <TwoFactorSettings />
      </div>
    </div>
  );
}
