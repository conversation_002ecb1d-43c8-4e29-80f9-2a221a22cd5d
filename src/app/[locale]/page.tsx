import { TodoClient } from '@/app/[locale]/todo-client';
import { AutoOneTap } from '@/components/auth/auto-one-tap';
import { auth } from '@/lib/auth';
import { getTranslations } from 'next-intl/server';
import { headers } from 'next/headers';
import { env } from '@/env';

export default async function Home() {
  const t = await getTranslations('HomePage');
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session) {
    return (
      <>
        {env.NEXT_PUBLIC_ENABLE_ONE_TAP && <AutoOneTap />}
        <div>{t('notLoggedIn')}</div>
      </>
    );
  }

  return <TodoClient />;
}
