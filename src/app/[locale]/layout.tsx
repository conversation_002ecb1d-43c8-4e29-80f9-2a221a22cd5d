import { routing } from '@/i18n/routing';
import { notFound } from 'next/navigation';
import { getMessages } from 'next-intl/server';
import { NextIntlClientProvider } from 'next-intl';
import { I18nProps } from '@/i18n/locale';
import { Gei<PERSON>, <PERSON>eist_Mono } from 'next/font/google';
import { AuthButtons } from '@/components/auth/auth-buttons';
import React from 'react';
const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export default async function LocaleLayout({
  children,
  params,
}: Readonly<{
  children: React.ReactNode;
  params: Promise<I18nProps>;
}>) {
  const { locale } = await params;
  console.log('locale', locale);
  if (!routing.locales.includes(locale as string)) {
    notFound();
  }

  const messages = await getMessages();

  return (
    <html lang={locale}>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <NextIntlClientProvider messages={messages}>
          <header className='flex justify-end items-center p-4 gap-4 h-16'>
            <AuthButtons />
          </header>
          {children}
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
