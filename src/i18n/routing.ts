import { defineRouting } from 'next-intl/routing';
import { createNavigation } from 'next-intl/navigation';
import {
  defaultLocale,
  localeDetection,
  localePrefix,
  locales,
} from '@/i18n/locale';

export const routing = defineRouting({
  locales: locales,
  defaultLocale: defaultLocale,
  localePrefix: localePrefix,
  localeDetection: localeDetection,
});

// Lightweight wrappers around Next.js' navigation APIs
// that will consider the routing configuration
export const { Link, redirect, usePathname, useRouter, getPathname } =
  createNavigation(routing);
