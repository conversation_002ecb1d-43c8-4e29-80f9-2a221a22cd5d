import { createAuthClient } from 'better-auth/react';
import {
  oneTapClient,
  twoFactorClient,
  adminClient,
} from 'better-auth/client/plugins';
import { env } from '@/env';

export const authClient = createAuthClient({
  plugins: [
    adminClient(),
    twoFactorClient({
      onTwoFactorRedirect() {
        // Redirect to 2FA verification page
        window.location.href = '/2fa';
      },
    }),
    ...(env.NEXT_PUBLIC_ENABLE_ONE_TAP
      ? [
          oneTapClient({
            clientId: env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
            autoSelect: false,
            cancelOnTapOutside: true,
            context: 'signin',
            promptOptions: {
              baseDelay: 1000, // Base delay in ms (default: 1000)
              maxAttempts: 5, // Maximum number of attempts before triggering onPromptNotification (default: 5)
            },
          }),
        ]
      : []),
  ],
});

export const {
  signIn,
  signUp,
  signOut,
  useSession,
  updateUser,
  changePassword,
  oneTap: _oneTap,
} = authClient;

// Export oneTap function that only works if enabled
export const oneTap = env.NEXT_PUBLIC_ENABLE_ONE_TAP
  ? _oneTap
  : () => {
      console.warn(
        'Google One Tap is disabled. Set NEXT_PUBLIC_ENABLE_ONE_TAP=true to enable it.'
      );
      return Promise.resolve();
    };
