import { betterAuth } from 'better-auth';
import { drizzleAdapter } from 'better-auth/adapters/drizzle';
import { nextCookies } from 'better-auth/next-js';
import { oneTap, twoFactor, admin } from 'better-auth/plugins';

import { db } from '@/db'; // your drizzle instance
import { schema } from '@/db/schema';
import { env } from '@/env';

export const auth = betterAuth({
  baseURL: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
  secret: process.env.BETTER_AUTH_SECRET || 'default-secret-key',
  appName: 'HAL Next Template', // Used as issuer for TOTP
  trustedOrigins: [
    'http://localhost:3000',
    'https://localhost:3000',
    env.NEXT_PUBLIC_APP_URL,
  ],
  database: drizzleAdapter(db, {
    provider: 'pg', // or "mysql", "sqlite"
    schema: schema,
  }),
  emailAndPassword: {
    enabled: true,
  },
  socialProviders: {
    google: {
      clientId: env.GOOGLE_CLIENT_ID,
      clientSecret: env.GOOGLE_CLIENT_SECRET,
    },
  },
  plugins: [
    nextCookies(),
    admin(),
    twoFactor(), // Add the two-factor plugin
    ...(env.NEXT_PUBLIC_ENABLE_ONE_TAP ? [oneTap()] : []), // Add the One Tap server plugin only if enabled
  ],
});
