/**
 * <PERSON><PERSON><PERSON> to create an admin user
 * Run with: npx tsx src/scripts/create-admin.ts
 */

import { auth } from '@/lib/auth';

async function createAdminUser() {
  try {
    const adminUser = await auth.api.signUpEmail({
      body: {
        name: 'Admin User',
        email: '<EMAIL>',
        password: 'admin123456',
      },
    });

    if (adminUser) {
      // Update user role to admin
      await auth.api.updateUser({
        body: {
          role: 'admin',
        },
        headers: {
          // This would need to be set with proper session headers
          // For now, you'll need to manually update the database
        },
      });

      console.log('Admin user created successfully!');
      console.log('Email: <EMAIL>');
      console.log('Password: admin123456');
      console.log('Please change the password after first login.');
    }
  } catch (error) {
    console.error('Error creating admin user:', error);
  }
}

createAdminUser();
